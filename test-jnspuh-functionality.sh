#!/bin/bash

# Script pour tester la fonctionnalité JNSPUH

echo "🚀 Test de la fonctionnalité JNSPUH - Bypass du tunnel d'inscription simplifié"
echo "============================================================================="

# Couleurs pour l'affichage
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les résultats
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Fonction pour afficher les informations
print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

echo ""
print_info "Vérification des fichiers modifiés..."

# Vérifier que les fichiers clés existent et contiennent les bonnes modifications
echo ""
echo "📁 Vérification des fichiers:"

# 1. UserProfile.java - méthode canSkipSimplifiedRegistrationTunnel
if grep -q "canSkipSimplifiedRegistrationTunnel" api/src/main/java/com/erhgo/domain/userprofile/UserProfile.java; then
    print_result 0 "UserProfile.java contient la méthode canSkipSimplifiedRegistrationTunnel"
else
    print_result 1 "UserProfile.java ne contient pas la méthode canSkipSimplifiedRegistrationTunnel"
fi

# 2. UserRegistrationState.java - enum JNSPUH_SEND_RESUME
if grep -q "JNSPUH_SEND_RESUME" api/src/main/java/com/erhgo/domain/userprofile/UserRegistrationState.java; then
    print_result 0 "UserRegistrationState.java contient l'enum JNSPUH_SEND_RESUME"
else
    print_result 1 "UserRegistrationState.java ne contient pas l'enum JNSPUH_SEND_RESUME"
fi

# 3. OpenAPI definition - nouveau champ
if grep -q "canSkipSimplifiedRegistrationTunnel" common/open-api-definition/user/user/paths/InitializeProfile.yaml; then
    print_result 0 "InitializeProfile.yaml contient le champ canSkipSimplifiedRegistrationTunnel"
else
    print_result 1 "InitializeProfile.yaml ne contient pas le champ canSkipSimplifiedRegistrationTunnel"
fi

# 4. UserProfileService.java - utilisation de la nouvelle méthode
if grep -q "canSkipSimplifiedRegistrationTunnel" api/src/main/java/com/erhgo/services/userprofile/UserProfileService.java; then
    print_result 0 "UserProfileService.java utilise la nouvelle méthode"
else
    print_result 1 "UserProfileService.java n'utilise pas la nouvelle méthode"
fi

# 5. HandicapAccountService.java - définition de l'état JNSPUH_SEND_RESUME
if grep -q "JNSPUH_SEND_RESUME" api/src/main/java/com/erhgo/services/HandicapAccountService.java; then
    print_result 0 "HandicapAccountService.java définit l'état JNSPUH_SEND_RESUME"
else
    print_result 1 "HandicapAccountService.java ne définit pas l'état JNSPUH_SEND_RESUME"
fi

# 6. Tests
if [ -f "api/src/test/java/com/erhgo/controller/UserEmailVerificationControllerTest.java" ]; then
    if grep -q "initialize_profile_should_allow_skip_tunnel_for_jnspuh_send_resume" api/src/test/java/com/erhgo/controller/UserEmailVerificationControllerTest.java; then
        print_result 0 "Tests pour JNSPUH ajoutés dans UserEmailVerificationControllerTest"
    else
        print_result 1 "Tests pour JNSPUH manquants dans UserEmailVerificationControllerTest"
    fi
else
    print_result 1 "UserEmailVerificationControllerTest.java non trouvé"
fi

# 7. Tests d'intégration
if [ -f "api/src/test/java/com/erhgo/integration/JnspuhRegistrationIntegrationTest.java" ]; then
    print_result 0 "Test d'intégration JNSPUH créé"
else
    print_result 1 "Test d'intégration JNSPUH manquant"
fi

# 8. Tests unitaires UserProfile
if [ -f "api/src/test/java/com/erhgo/domain/userprofile/UserProfileJnspuhTest.java" ]; then
    print_result 0 "Tests unitaires UserProfile JNSPUH créés"
else
    print_result 1 "Tests unitaires UserProfile JNSPUH manquants"
fi

echo ""
print_info "Vérification de la génération OpenAPI..."

# Vérifier si le DTO généré contient le nouveau champ
if [ -f "api/target/generated-sources/openapi/src/gen/java/main/com/erhgo/openapi/dto/InitializedProfileDTO.java" ]; then
    if grep -q "canSkipSimplifiedRegistrationTunnel" api/target/generated-sources/openapi/src/gen/java/main/com/erhgo/openapi/dto/InitializedProfileDTO.java; then
        print_result 0 "InitializedProfileDTO généré contient le nouveau champ"
    else
        print_result 1 "InitializedProfileDTO généré ne contient pas le nouveau champ"
    fi
else
    print_result 1 "InitializedProfileDTO.java non généré"
fi

echo ""
print_info "Résumé de la fonctionnalité JNSPUH:"
echo ""
echo "🎯 Objectif: Permettre aux utilisateurs JNSPUH de bypasser le tunnel d'inscription simplifié"
echo ""
echo "📋 Fonctionnalités implémentées:"
echo "   • Nouvel état de registration: JNSPUH_SEND_RESUME"
echo "   • Méthode canSkipSimplifiedRegistrationTunnel() dans UserProfile"
echo "   • Nouveau champ dans l'API InitializeProfile"
echo "   • Mise à jour du HandicapAccountService pour définir l'état après traitement CV"
echo "   • Tests complets (unitaires, intégration, API)"
echo ""
echo "🔄 Flux:"
echo "   1. Utilisateur JNSPUH envoie son CV via HandicapAccountService"
echo "   2. Après traitement réussi du CV, état = JNSPUH_SEND_RESUME"
echo "   3. Appel à /user/initialize-profile retourne canSkipSimplifiedRegistrationTunnel: true"
echo "   4. Frontend peut bypasser le tunnel d'inscription simplifié"
echo ""
echo "✅ Implémentation terminée et prête pour les tests!"
